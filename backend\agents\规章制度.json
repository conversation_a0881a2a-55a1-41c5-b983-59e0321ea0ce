{"nodes": [{"data": {"form": {"prologue": "你好！ 我是你的助理，有什么可以帮到你的吗？", "query": []}, "label": "<PERSON><PERSON>", "name": "begin"}, "dragging": false, "id": "begin", "measured": {"height": 44, "width": 200}, "position": {"x": 34, "y": 251}, "selected": false, "sourcePosition": "left", "targetPosition": "right", "type": "beginNode"}, {"data": {"form": {}, "label": "Answer", "name": "获取用户输入"}, "dragging": false, "id": "Answer:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "measured": {"height": 44, "width": 200}, "position": {"x": 279.5, "y": 250.5}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "type": "logicNode"}, {"data": {"form": {"kb_ids": ["d849051e41dc11f098c30242ac150006"], "keywords_similarity_weight": 0.3, "query": [{"component_id": "Answer:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "reference"}], "similarity_threshold": 0.2, "top_n": 8, "use_kg": true}, "label": "Retrieval", "name": "知识检索_0"}, "dragging": false, "id": "Retrieval:LovelyRiversHeal", "measured": {"height": 106, "width": 200}, "position": {"x": 782.6288233352695, "y": 235.75957829707508}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "type": "retrievalNode"}, {"data": {"form": {"cite": true, "frequencyPenaltyEnabled": true, "frequency_penalty": 0.7, "llm_id": "deepseek-r1:32b@Ollama", "maxTokensEnabled": false, "max_tokens": 256, "message_history_window_size": 12, "parameter": "Precise", "parameters": [], "presencePenaltyEnabled": true, "presence_penalty": 0.4, "prompt": "你是专业的电力行业规章制度专家助手，具备以下特征：\n\n\n\n**专业背景**：\n\n- 深度了解电力行业安全管理制度\n\n- 熟悉网络安全运维操作规程  \n\n- 掌握应急响应处置流程\n\n- 精通权限管理和审计要求\n\n\n\n**回答规范**：\n\n1. **直接回答**：首先给出核心答案\n\n2. **制度依据**：引用具体制度条款和章节号\n\n3. **操作要点**：列出关键操作步骤\n\n4. **风险提示**：如有风险，必须明确标注⚠️\n\n5. **相关参考**：提供相关制度链接\n\n\n\n**格式要求**：\n\n- 使用 **粗体** 强调重要信息\n\n- 使用 🔴 标记高风险项\n\n- 使用 ⚠️ 标记注意事项\n\n- 使用 ✅ 标记必做项\n\n\n\n**引用格式**：\n\n> **依据**：《具体制度名称》第X.X条\n\n> **原文**：[具体条款内容]\n\n\n\n请严格按照以上规范回答用户关于规章制度的问题。\n\n知识检索内容如下：\n\n {Retrieval:LovelyRiversHeal}", "temperature": 0.1, "temperatureEnabled": true, "topPEnabled": true, "top_p": 0.3}, "label": "Generate", "name": "生成回答_0"}, "dragging": false, "id": "Generate:FiveBusesTrain", "measured": {"height": 108, "width": 200}, "position": {"x": 546.1042493862665, "y": 524.5660341899303}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "type": "generateNode"}], "edges": [{"id": "xy-edge__begin-Answer:<PERSON><PERSON><PERSON>liensTicklec", "markerEnd": "logo", "source": "begin", "style": {"stroke": "rgb(202 197 245)", "strokeWidth": 2}, "target": "Answer:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "targetHandle": "c", "type": "buttonEdge", "zIndex": 1001}, {"id": "xy-edge__Answer:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ickleb-Retrieval:LovelyRiversHealc", "markerEnd": "logo", "source": "Answer:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sourceHandle": "b", "style": {"stroke": "rgb(202 197 245)", "strokeWidth": 2}, "target": "Retrieval:LovelyRiversHeal", "targetHandle": "c", "type": "buttonEdge", "zIndex": 1001}, {"id": "xy-edge__Retrieval:LovelyRiversHealb-Generate:FiveBusesTrainb", "markerEnd": "logo", "source": "Retrieval:LovelyRiversHeal", "sourceHandle": "b", "style": {"stroke": "rgb(202 197 245)", "strokeWidth": 2}, "target": "Generate:FiveBusesTrain", "targetHandle": "b", "type": "buttonEdge", "zIndex": 1001}, {"id": "xy-edge__Generate:FiveBusesTrainc-Answer:SixtyAliensTicklec", "markerEnd": "logo", "source": "Generate:FiveBusesTrain", "sourceHandle": "c", "style": {"stroke": "rgb(202 197 245)", "strokeWidth": 2}, "target": "Answer:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "targetHandle": "c", "type": "buttonEdge", "zIndex": 1001}]}