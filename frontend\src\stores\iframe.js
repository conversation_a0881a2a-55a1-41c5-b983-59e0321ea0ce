import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { useRouter, useRoute } from 'vue-router';

export const useIframeStore = defineStore('iframe', () => {
  // Router
  const router = useRouter();
  const route = useRoute();
  
  // State
  const sidebarCollapsed = ref(false);
  
  // Getters
  const currentRouteMeta = computed(() => {
    return route.meta || {};
  });
  
  // Actions
  function navigateTo(routeName) {
    router.push({ name: routeName });
  }
  
  function toggleSidebar() {
    sidebarCollapsed.value = !sidebarCollapsed.value;
  }
  
  return {
    // State
    sidebarCollapsed,
    
    // Getters
    currentRouteMeta,
    
    // Actions
    navigateTo,
    toggleSidebar
  };
});