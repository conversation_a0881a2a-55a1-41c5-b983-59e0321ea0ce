<!DOCTYPE html>
<html lang="">
  <head>
    <meta charset="UTF-8">
    <link rel="icon" href="/favicon.ico">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Write Helper</title>
    <!-- 注意: 页面标题将由应用程序动态设置，基于 src/config.js 中的配置 -->
    <style>
      /* 确保根元素和body不会有内置限制 */
      html, body {
        margin: 0;
        padding: 0;
        width: 100%;
        height: 100%;
        overflow: hidden;
      }
      #app {
        width: 100%;
        height: 100%;
        margin: 0;
        padding: 0;
      }
    </style>
  </head>
  <body>
    <div id="app"></div>
    <script type="module" src="/src/main.js"></script>
    <!-- 
      配置文件位于 src/config.js
      构建后，可以修改该文件来适配不同的环境
      无需重新构建应用
    -->
  </body>
</html>
