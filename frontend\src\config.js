/**
 * 全局配置文件
 * 包含应用中使用的各种配置参数
 * 在构建后可以修改此文件以适配不同环境
 */
// 123
// iframe相关配置
export const iframeConfig = {
  // 规章制度问答助手
  rules: {
    baseUrl: 'http://10.100.98.181:8011/chat/share',
    sharedId: '7dce094a411411f098910242ac150006',
    from: 'agent',
    auth: 'NhYWY1NGZlNDFiMjExZjBiZTc5MDI0Mm'
  },

  // 安全报告周报助手
  safetyWeekly: {
    baseUrl: 'http://10.100.98.181:8011/chat/share',
    sharedId: 'bac6beb641da11f0a3750242ac150006', // 更新为实际的周报助手ID
    from: 'agent',
    auth: 'NhYWY1NGZlNDFiMjExZjBiZTc5MDI0Mm' // 使用与规章制度问答助手相同的认证token
  },
  // 安全报告新闻稿助手
  safetyNewsletter: {
    baseUrl: 'http://10.100.98.181:8011/chat/share',
    sharedId: 'bd9c89e844f711f0b4330242ac150006', // 更新为实际的新闻稿助手ID
    from: 'agent',
    auth: 'NhYWY1NGZlNDFiMjExZjBiZTc5MDI0Mm' // 使用与规章制度问答助手相同的认证token
  },

  // 权限生命周期管理助手
  permissionLifecycle: {
    baseUrl: 'http://10.100.98.181:8011/chat/share',
    sharedId: '3e1927dc71b911f095190242ac170007',
    from: 'agent',
    auth: 'NhYWY1NGZlNDFiMjExZjBiZTc5MDI0Mm'
  }
};

// 其他系统配置
export const systemConfig = {
  title: 'AI写作助手',
  defaultLocale: 'zh-CN',
  // 图谱查看配置
  graphUrl: 'http://10.100.98.181:7474/browser/'
};

// 导出默认配置
export default {
  iframe: iframeConfig,
  system: systemConfig
};
