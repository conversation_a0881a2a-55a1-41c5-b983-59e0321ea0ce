{"nodes": [{"data": {"label": "<PERSON><PERSON>", "name": "begin"}, "dragging": false, "id": "begin", "measured": {"height": 44, "width": 200}, "position": {"x": -163.9446504186442, "y": 240.15491056887222}, "selected": false, "sourcePosition": "left", "targetPosition": "right", "type": "beginNode"}, {"data": {"form": {}, "label": "Answer", "name": "对话"}, "dragging": false, "id": "Answer:LegalMailsDoubt", "measured": {"height": 44, "width": 200}, "position": {"x": 134.5, "y": 239.5}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "type": "logicNode"}, {"data": {"form": {"cite": true, "frequencyPenaltyEnabled": true, "frequency_penalty": 0.7, "llm_id": "deepseek-r1:32b@Ollama", "maxTokensEnabled": false, "max_tokens": 256, "message_history_window_size": 12, "parameter": "Precise", "parameters": [], "presencePenaltyEnabled": true, "presence_penalty": 0.4, "prompt": "\n你是一个专业的网络安全工作报告生成助手。请按照以下流程与用户互动，生成规范的安全运营/技防建设/渗透攻击工作简报：\n\n交互流程：\n\n第一步：收集关键信息\n\n请主动向用户请求以下关键信息：\n\n基础信息：\n\n报告时间段（起始日期-结束日期）\n\n负责人员姓名（甲、乙、丙的具体姓名）\n\n重点工作信息：\n\n重点项目名称（1-5个）\n\n每个项目的本周完成情况\n\n每个项目的下周计划\n\n常态工作数据：\n\n本周手动封禁IP数量\n\n因国网攻击情况预警封禁IP数量\n\n因威胁响应要求封禁IP数量\n\n因流量异常放开平台告警封禁IP数量\n\n本周收到国网攻击源预警数量\n\n本周发现漏洞数量\n\n本周闭环漏洞数量\n\n本周审核交通策略数量\n\n其他信息：\n\n项目推进情况（如有）\n\n存在的问题及建议（如有）\n\n下月工作计划\n\n第二步：信息完整性检查\n\n收到用户信息后，检查以下必要信息是否完整：\n\n时间段 ✓/✗\n\n人员信息 ✓/✗\n\n重点工作 ✓/✗\n\n常态工作数据 ✓/✗\n\n第三步：处理不完整信息\n\n如果信息不完整：\n\n明确指出缺失的信息项\n\n询问用户是希望补充信息还是使用默认值生成\n\n如用户选择补充，重新请求缺失信息\n\n如用户选择默认生成，使用合理的占位符（如\"待补充\"、\"0\"等）\n\n第四步：生成简报\n\n信息收集完成后，按照以下模板生成简报：\n\n生成模板：\n\n标题格式\n\n安全运营/技防建设/渗透攻击工作周报\n（[起始日期]-[结束日期]）\n\n\n报告结构\n\n一、重点工作完成情况\n\n1. [项目名称]（[负责人姓名]）\n本周工作：[具体完成内容]\n下周计划：[计划安排]\n\n2. [项目名称]（[负责人姓名]）  \n本周工作：[具体完成内容]\n下周计划：[计划安排]\n\n[继续其他项目...]\n\n\n二、常态工作完成情况\n\n本周完成情况\n\n(1) 网络攻击情况：（整体） 公司网络、系统和数据整体安全可控，未发生重大网络安全事件，黄金运营小组响应和分析处置更联防联控任务等。\n\n常态化安全运营保障工作，本周手动封禁IP [X] 个，其中因国网攻击的情况预警封禁IP [X] 个，因威胁响应要求封禁IP [X] 个，因流量异常放开平台告警封禁IP [X] 个。\n\n(2) 风险预警情况：（[负责人姓名]） 本周收到国网攻击源预警 [X] 起。\n\n(3) 漏洞整改情况：（[负责人姓名]）\n本周发现漏洞 [X] 个，本周闭环漏洞 [X] 个。\n\n(4) 每日防火墙策略审核：（[负责人姓名]） 对每日防火墙策略更新策略进行审核，本周共审核交通策略 [X] 条，无新增网络外界策略等。\n\n下月工作计划 (1) 常态研判分析工作：（[负责人姓名]）\n\n三、项目推进情况 \n\n[项目进展描述或\"无\"]\n\n四、问题及建议\n[问题及建议或\"无\"]\n\n格式要求\n\n使用规范的中文标点符号\n\n数据要准确具体\n\n语言简洁专业\n\n层次结构清晰\n\n请根据用户输入的内容 {Answer:LegalMailsDoubt}判断到了流程的哪一步，对用户的话进行回复。", "temperature": 0, "temperatureEnabled": true, "topPEnabled": true, "top_p": 0.3}, "label": "Generate", "name": "生成回答"}, "dragging": false, "id": "Generate:PrettyBroomsDesign", "measured": {"height": 108, "width": 200}, "position": {"x": 306.5843739821056, "y": 392.56986178173076}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "type": "generateNode"}], "edges": [{"id": "xy-edge__begin-Answer:LegalMailsDoubtc", "markerEnd": "logo", "source": "begin", "style": {"stroke": "rgb(202 197 245)", "strokeWidth": 2}, "target": "Answer:LegalMailsDoubt", "targetHandle": "c", "type": "buttonEdge", "zIndex": 1001}, {"id": "xy-edge__Generate:PrettyBroomsDesignc-Answer:LegalMailsDoubtc", "markerEnd": "logo", "source": "Generate:PrettyBroomsDesign", "sourceHandle": "c", "style": {"stroke": "rgb(202 197 245)", "strokeWidth": 2}, "target": "Answer:LegalMailsDoubt", "targetHandle": "c", "type": "buttonEdge", "zIndex": 1001}, {"id": "xy-edge__Answer:LegalMailsDoubtb-Generate:PrettyBroomsDesignb", "markerEnd": "logo", "source": "Answer:LegalMailsDoubt", "sourceHandle": "b", "style": {"stroke": "rgb(202 197 245)", "strokeWidth": 2}, "target": "Generate:PrettyBroomsDesign", "targetHandle": "b", "type": "buttonEdge", "zIndex": 1001}]}