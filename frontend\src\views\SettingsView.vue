<template>
  <div class="settings-view">
    <div class="settings-container">
      <div class="settings-header">
        <h2 class="settings-title">系统设置</h2>
        <p class="settings-description">配置应用程序的各项设置</p>
      </div>
      
      <div class="settings-sections">
        <div class="settings-section">
          <h3 class="section-title">显示设置</h3>
          
          <div class="setting-item">
            <div class="setting-info">
              <h4 class="setting-name">侧边栏默认状态</h4>
              <p class="setting-description">设置应用启动时侧边栏的默认状态</p>
            </div>
            <div class="setting-control">
              <select v-model="sidebarDefault" class="select-control">
                <option value="expanded">展开</option>
                <option value="collapsed">折叠</option>
              </select>
            </div>
          </div>
          
          <div class="setting-item">
            <div class="setting-info">
              <h4 class="setting-name">主题</h4>
              <p class="setting-description">选择应用的显示主题</p>
            </div>
            <div class="setting-control">
              <select v-model="theme" class="select-control">
                <option value="light">浅色</option>
                <option value="dark">深色</option>
                <option value="system">跟随系统</option>
              </select>
            </div>
          </div>
        </div>
        
        <div class="settings-section">
          <h3 class="section-title">API设置</h3>
          
          <div class="setting-item">
            <div class="setting-info">
              <h4 class="setting-name">API基础URL</h4>
              <p class="setting-description">设置API服务器的基础URL</p>
            </div>
            <div class="setting-control">
              <input type="text" v-model="apiBaseUrl" class="input-control" placeholder="http://example.com/api" />
            </div>
          </div>
          
          <div class="setting-item">
            <div class="setting-info">
              <h4 class="setting-name">图谱查看URL</h4>
              <p class="setting-description">设置图谱查看页面的URL</p>
            </div>
            <div class="setting-control">
              <input type="text" v-model="graphUrl" class="input-control" placeholder="http://*************:7474/browser/" />
            </div>
          </div>
        </div>
        
        <div class="settings-section">
          <h3 class="section-title">关于</h3>
          
          <div class="about-info">
            <p><strong>AI写作助手</strong></p>
            <p>版本: 1.0.0</p>
            <p>© 2023 AI写作助手团队</p>
          </div>
        </div>
      </div>
      
      <div class="settings-actions">
        <button class="save-btn" @click="saveSettings">保存设置</button>
        <button class="reset-btn" @click="resetSettings">重置默认</button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { useIframeStore } from '../stores/iframe';
import { systemConfig } from '../config';

const store = useIframeStore();

// 设置状态
const sidebarDefault = ref(store.sidebarCollapsed ? 'collapsed' : 'expanded');
const theme = ref('light');
const apiBaseUrl = ref('http://*************:8011');
const graphUrl = ref(systemConfig.graphUrl);

// 保存设置
const saveSettings = () => {
  // 更新侧边栏状态
  if (sidebarDefault.value === 'collapsed' && !store.sidebarCollapsed) {
    store.toggleSidebar();
  } else if (sidebarDefault.value === 'expanded' && store.sidebarCollapsed) {
    store.toggleSidebar();
  }
  
  // 更新图谱URL配置
  systemConfig.graphUrl = graphUrl.value;
  
  alert('设置已保存');
};

// 重置设置
const resetSettings = () => {
  sidebarDefault.value = 'expanded';
  theme.value = 'light';
  apiBaseUrl.value = 'http://*************:8011';
  graphUrl.value = 'http://*************:7474/browser/';
  
  if (store.sidebarCollapsed) {
    store.toggleSidebar();
  }
  
  alert('设置已重置为默认值');
};
</script>

<style scoped>
.settings-view {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  background-color: #f8fafc;
  padding: 2rem;
}

.settings-container {
  max-width: 800px;
  margin: 0 auto;
  width: 100%;
}

.settings-header {
  margin-bottom: 2rem;
}

.settings-title {
  font-size: 1.875rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 0.5rem 0;
}

.settings-description {
  color: #64748b;
  font-size: 1rem;
  margin: 0;
}

.settings-sections {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.settings-section {
  background: white;
  border-radius: 0.75rem;
  padding: 1.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 1.5rem 0;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid #e2e8f0;
}

.setting-item {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  padding: 1rem 0;
  border-bottom: 1px solid #f1f5f9;
}

.setting-item:last-child {
  border-bottom: none;
}

.setting-info {
  flex: 1;
  padding-right: 2rem;
}

.setting-name {
  font-size: 1rem;
  font-weight: 500;
  color: #1e293b;
  margin: 0 0 0.5rem 0;
}

.setting-description {
  color: #64748b;
  font-size: 0.875rem;
  margin: 0;
}

.setting-control {
  width: 200px;
}

.select-control, .input-control {
  width: 100%;
  padding: 0.5rem;
  border-radius: 0.375rem;
  border: 1px solid #e2e8f0;
  background-color: white;
  font-size: 0.875rem;
  color: #1e293b;
}

.select-control:focus, .input-control:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

.about-info {
  color: #64748b;
  font-size: 0.875rem;
  line-height: 1.6;
}

.settings-actions {
  display: flex;
  gap: 1rem;
  margin-top: 2rem;
  justify-content: flex-end;
}

.save-btn {
  padding: 0.5rem 1rem;
  background-color: #3b82f6;
  color: white;
  border: none;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.save-btn:hover {
  background-color: #2563eb;
}

.reset-btn {
  padding: 0.5rem 1rem;
  background-color: white;
  color: #64748b;
  border: 1px solid #e2e8f0;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.reset-btn:hover {
  background-color: #f1f5f9;
  color: #475569;
}

@media (max-width: 768px) {
  .settings-view {
    padding: 1rem;
  }
  
  .setting-item {
    flex-direction: column;
    gap: 1rem;
  }
  
  .setting-control {
    width: 100%;
  }
  
  .setting-info {
    padding-right: 0;
  }
}
</style> 
