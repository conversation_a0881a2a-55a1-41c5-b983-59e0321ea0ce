import { createApp } from 'vue';
import { createPinia } from 'pinia';
import { createRouter, createWebHistory } from 'vue-router';
import App from './App.vue';
import './assets/main.css';
import routes from './router';
import { systemConfig } from './config';

// 设置页面标题
document.title = systemConfig.title;

const app = createApp(App);
const pinia = createPinia();

// Create router instance
const router = createRouter({
  history: createWebHistory(),
  routes
});

app.use(pinia);
app.use(router);
app.mount('#app');