{"nodes": [{"data": {"label": "<PERSON><PERSON>", "name": "begin"}, "dragging": false, "id": "begin", "measured": {"height": 44, "width": 200}, "position": {"x": -301.5, "y": 60}, "selected": false, "sourcePosition": "left", "targetPosition": "right", "type": "beginNode"}, {"data": {"form": {}, "label": "Answer", "name": "对话"}, "dragging": false, "id": "Answer:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "measured": {"height": 44, "width": 200}, "position": {"x": -52, "y": 59}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "type": "logicNode"}, {"data": {"form": {"kb_ids": ["a95df16c44fa11f094ec0242ac150006"], "kb_vars": [], "keywords_similarity_weight": 0.3, "query": [{"component_id": "KeywordExtract:RottenResultsShop", "type": "reference"}], "similarity_threshold": 0.2, "top_n": 8, "use_kg": false}, "label": "Retrieval", "name": "知识检索"}, "dragging": false, "id": "Retrieval:WildHumansItch", "measured": {"height": 106, "width": 200}, "position": {"x": 131.84024604461356, "y": 209.5}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "type": "retrievalNode"}, {"data": {"form": {"cite": true, "frequencyPenaltyEnabled": true, "frequency_penalty": 0.7, "llm_id": "deepseek-r1:32b@Ollama", "maxTokensEnabled": false, "max_tokens": 256, "message_history_window_size": 12, "parameter": "Precise", "parameters": [], "presencePenaltyEnabled": true, "presence_penalty": 0.4, "prompt": "你是一位专业的电网企业新闻稿撰写专家，专门为国网盐城供电公司及下属分公司撰写内部新闻稿。\n\n\n\n核心原则：严格忠实于源材料\n\n重要约束：你必须严格基于用户提供的信息进行写作，不得添加、推测或编造任何未在原材料中明确提及的内容，包括但不限于：\n\n具体的技术参数、投资金额、设备数量\n\n具体的时间细节（如果原材料中未明确则不指明具体时间）\n\n具体的人员姓名（如果原材料中未提及则不指明具体姓名）\n\n具体的技术效果或数据指标\n\n详细的实施过程描述（如果原材料中未详述，则无需进行详细的实施过程描述）\n\n\n\n输入类型识别与处理\n\n根据用户提供的材料类型，采用相应的写作策略：\n\n1. 会议纪要类\n\n重点提取：会议主题、时间、地点、主要成果、后续安排\n\n转换策略：将会议过程转化为具有新闻价值的事件报道\n\n避免：过度展开技术细节，添加未提及的具体效果\n\n2. 工作总结类\n\n重点提取：工作内容、完成情况、主要成果、意义价值\n\n转换策略：突出工作亮点和实际效果\n\n避免：夸大效果，添加未确认的数据\n\n新闻稿标准格式\n\n1. 标题格式\n\n格式： 部门/分公司名称：工作内容概述\n\n标题简洁有力，基于材料中的核心内容\n\n使用粗体格式\n\n如材料中未明确部门，可用\"公司\"替代\n\n2. 基本信息栏\n\n作者 [如材料中有具体人员则填写，否则写\"相关人员\"] 来源\n\n创建时间 [基于材料时间或当前时间] 显示时间 [同创建时间]\n\n3. 正文结构（段落式，无小标题）\n\n导语段落（第1段）\n\n以材料中的时间信息开头\n\n概述事件的核心内容和重要意义\n\n基于材料内容，不添加额外描述\n\n主体段落（第2-3段）\n\n如是会议类：重点描述会议议题、参与情况、主要讨论内容\n\n如是工作类：描述工作背景、实施过程、取得成果\n\n严格基于材料内容，保持客观描述\n\n如材料中有具体数据则使用，无则不添加\n\n结尾段落（最后1段）\n\n以\"据悉\"、\"后续\"、\"下一步\"开头\n\n基于材料中的后续安排或既定价值进行描述\n\n如材料中无明确后续计划，则着重强调工作意义\n\n4. 责任编辑\n\n（责任编辑：[如材料中有则填写，否则用\"相关编辑\"]）\n\n写作风格要求\n\n语言规范\n\n第三人称客观叙述\n\n正式、严谨的官方语言\n\n使用电网行业常用术语\n\n避免主观评价和情感色彩\n\n内容约束\n\n数据使用： 仅使用材料中明确提及的数据，不得估算或推测\n\n时间表述： 基于材料中的确切时间，不得模糊化处理\n\n人员信息： 仅使用材料中明确的人员信息\n\n技术细节： 不得超出材料范围添加技术描述\n\n常用表述模式\n\n时间表述\n\n根据材料：具体日期/\"近日\"/\"日前\"\n\n避免：自行推测时间范围\n\n工作描述\n\n\"成功举办\"、\"顺利完成\"、\"积极开展\"\n\n基于材料内容选择合适动词\n\n意义表述\n\n\"为...提供支撑\"、\"推动...发展\"、\"促进...提升\"\n\n基于材料中体现的实际价值\n\n质量检查清单：\n\n写作完成后，请自检：\n\n 是否严格基于提供材料？\n\n 是否添加了材料中未提及的信息？\n\n 数据和时间是否准确？\n\n 语言是否符合官方文件规范？\n\n 结构是否完整清晰？\n\n现在用户提供的新闻稿相关信息如下：\n{Answer:CommonAreasMatter}\n\n知识库内的参考信息如下：\n\n{Retrieval:WildHumansItch}", "temperature": 0.1, "temperatureEnabled": true, "topPEnabled": true, "top_p": 0.3}, "label": "Generate", "name": "生成回答"}, "dragging": false, "id": "Generate:CleanBathsJam", "measured": {"height": 108, "width": 200}, "position": {"x": -159, "y": 207.5}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "type": "generateNode"}, {"data": {"form": {"frequencyPenaltyEnabled": true, "frequency_penalty": 0.7, "llm_id": "qwen2:7b@Ollama", "maxTokensEnabled": false, "max_tokens": 256, "parameter": "Precise", "presencePenaltyEnabled": true, "presence_penalty": 0.4, "query": [{"component_id": "Answer:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "reference"}], "temperature": 0.1, "temperatureEnabled": true, "topPEnabled": true, "top_n": 3, "top_p": 0.3}, "label": "KeywordExtract", "name": "关键词_0"}, "dragging": false, "id": "KeywordExtract:RottenResultsShop", "measured": {"height": 108, "width": 200}, "position": {"x": 241.63057991838278, "y": 28.483128193673963}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "type": "keywordNode"}], "edges": [{"id": "xy-edge__begin-Answer:CommonAreasMatterc", "markerEnd": "logo", "source": "begin", "style": {"stroke": "rgb(202 197 245)", "strokeWidth": 2}, "target": "Answer:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "targetHandle": "c", "type": "buttonEdge", "zIndex": 1001}, {"id": "xy-edge__Retrieval:WildHumansItchc-Generate:CleanBathsJamb", "markerEnd": "logo", "source": "Retrieval:WildHumansItch", "sourceHandle": "c", "style": {"stroke": "rgb(202 197 245)", "strokeWidth": 2}, "target": "Generate:CleanBathsJam", "targetHandle": "b", "type": "buttonEdge", "zIndex": 1001}, {"id": "xy-edge__Generate:CleanBathsJamc-Answer:CommonAreasMatterc", "markerEnd": "logo", "source": "Generate:CleanBathsJam", "sourceHandle": "c", "style": {"stroke": "rgb(202 197 245)", "strokeWidth": 2}, "target": "Answer:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "targetHandle": "c", "type": "buttonEdge", "zIndex": 1001}, {"id": "xy-edge__Answer:CommonAreasMatterb-KeywordExtract:RottenResultsShopc", "markerEnd": "logo", "source": "Answer:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sourceHandle": "b", "style": {"stroke": "rgb(202 197 245)", "strokeWidth": 2}, "target": "KeywordExtract:RottenResultsShop", "targetHandle": "c", "type": "buttonEdge", "zIndex": 1001}, {"id": "xy-edge__KeywordExtract:RottenResultsShopb-Retrieval:WildHumansItchb", "markerEnd": "logo", "source": "KeywordExtract:RottenResultsShop", "sourceHandle": "b", "style": {"stroke": "rgb(202 197 245)", "strokeWidth": 2}, "target": "Retrieval:WildHumansItch", "targetHandle": "b", "type": "buttonEdge", "zIndex": 1001}]}