import GeneralAssistantView from '../views/GeneralAssistantView.vue';
import ProfessionalAssistantView from '../views/ProfessionalAssistantView.vue';
import PermissionLifecycleView from '../views/PermissionLifecycleView.vue';

const routes = [
  {
    path: '/',
    redirect: '/rules'
  },
  {
    path: '/rules',
    name: 'rules',
    component: GeneralAssistantView,
    meta: {
      title: '规章制度问答助手',
      icon: 'MessageSquare'
    }
  },
  {
    path: '/safety',
    name: 'safety',
    component: ProfessionalAssistantView,
    meta: {
      title: '安全报告助手',
      icon: 'Bot'
    }
  },
  {
    path: '/permission',
    name: 'permission',
    component: PermissionLifecycleView,
    meta: {
      title: '权限生命周期管理助手',
      icon: 'Shield'
    }
  }
];

export default routes;
