# Python 通用忽略
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/
cover/

# Translations
*.mo
*.pot

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# FastAPI 特定忽略
*.log  # FastAPI 日志文件
local_settings.py  # 本地配置文件
*.sqlite3  # SQLite 数据库（如 db.sqlite3、db.sqlite3-journal）
migrations/  # 数据库迁移文件（如 Alembic）

# Vue.js 前端忽略
node_modules/  # Node.js 依赖
dist/  # Vue 构建输出目录
.cache/  # Vite 或 Webpack 缓存
*.local  # 本地开发配置文件
.vscode/  # VS Code 配置文件（可选）
npm-debug.log  # npm 日志
yarn-error.log  # Yarn 日志
*.env.local  # Vue 环境变量文件
*.env.development  # 开发环境变量
*.env.production  # 生产环境变量

# Django（如果使用，保留；否则可删除）
db.sqlite3
db.sqlite3-journal

# Flask（如果不使用，可删除）
instance/
.webassets-cache

# Scrapy（如果不使用，可删除）
.scrapy

# Sphinx 文档
docs/_build/

# PyBuilder
.pybuilder/
target/

# Jupyter Notebook（如果不使用，可删除）
.ipynb_checkpoints

# IPython（如果不使用，可删除）
profile_default/
ipython_config.py

# pyenv（根据需要保留或删除）
#.python-version

# pipenv（根据需要保留或删除）
#Pipfile.lock

# PEP 582
__pypackages__/

# Celery（如果不使用，可删除）
celerybeat-schedule
celerybeat.pid

# SageMath
*.sage.py

# Spyder
.spyderproject
.spyproject

# Rope
.ropeproject

# mkdocs
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre
.pyre/

# pytype
.pytype/

# Cython
cython_debug/
