<template>
  <div class="professional-assistant-view">
    <!-- 助手选择器 -->
    <div class="assistant-selector" v-if="!selectedAssistant">
      <div class="selector-header">
        <h2 class="selector-title">安全报告助手</h2>
        <p class="selector-description">选择您需要的助手类型</p>
      </div>
      
      <div class="assistant-cards">
        <div 
          class="assistant-card"
          :class="{ active: selectedAssistant === 'weekly' }"
          @click="selectAssistant('weekly')"
        >
          <div class="card-icon">
            <CalendarIcon class="icon" />
          </div>
          <div class="card-content">
            <h3 class="card-title">周报助手</h3>
            <p class="card-description">协助生成安全周报，整理安全事件和趋势分析</p>
          </div>
          <div class="card-indicator" v-if="selectedAssistant === 'weekly'">
            <CheckIcon class="check-icon" />
          </div>
        </div>
        
        <div 
          class="assistant-card"
          :class="{ active: selectedAssistant === 'newsletter' }"
          @click="selectAssistant('newsletter')"
        >
          <div class="card-icon">
            <NewspaperIcon class="icon" />
          </div>
          <div class="card-content">
            <h3 class="card-title">新闻稿助手</h3>
            <p class="card-description">协助撰写安全新闻稿，发布安全公告和通知</p>
          </div>
          <div class="card-indicator" v-if="selectedAssistant === 'newsletter'">
            <CheckIcon class="check-icon" />
          </div>
        </div>
      </div>
    </div>
    
    <!-- iframe容器 -->
    <div class="iframe-container" v-if="selectedAssistant">
      <div class="iframe-header">
        <div class="header-info">
          <h2 class="header-title">安全报告助手</h2>
          <div class="assistant-badge">
            <component :is="currentAssistantIcon" class="badge-icon" />
            <span class="badge-text">{{ currentAssistantTitle }}</span>
          </div>
        </div>
        <button 
          class="change-assistant-btn"
          @click="selectedAssistant = null"
          title="切换助手"
        >
          <RefreshCwIcon class="btn-icon" />
          切换助手
        </button>
      </div>
      
      <div class="iframe-wrapper">
        <iframe
          :src="iframeUrl"
          :key="selectedAssistant + '-' + reloadCounter"
          class="chat-iframe"
          frameborder="0"
        ></iframe>
      </div>
    </div>
    
    <!-- 空状态 -->
    <div class="empty-state" v-else>
      <div class="empty-content">
        <BotIcon class="empty-icon" />
        <h3 class="empty-title">请选择助手类型</h3>
        <p class="empty-description">选择上方的助手卡片开始使用</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';
import { CalendarIcon, NewspaperIcon, CheckIcon, RefreshCwIcon, BotIcon } from 'lucide-vue-next';
import { iframeConfig } from '../config';

// 选中的助手类型
const selectedAssistant = ref(null);
// 用于强制重新加载iframe的计数器
const reloadCounter = ref(0);

// 助手配置映射
const assistantConfigs = {
  weekly: {
    title: '周报助手',
    icon: CalendarIcon,
    config: 'safetyWeekly'
  },
  newsletter: {
    title: '新闻稿助手', 
    icon: NewspaperIcon,
    config: 'safetyNewsletter'
  }
};

// 当前助手信息
const currentAssistantTitle = computed(() => {
  return selectedAssistant.value ? assistantConfigs[selectedAssistant.value].title : '';
});

const currentAssistantIcon = computed(() => {
  return selectedAssistant.value ? assistantConfigs[selectedAssistant.value].icon : null;
});

// 生成iframe URL
const iframeUrl = computed(() => {
  if (!selectedAssistant.value) return '';
  
  const configKey = assistantConfigs[selectedAssistant.value].config;
  const config = iframeConfig[configKey];
  const params = new URLSearchParams();
  
  if (config.sharedId) params.append('shared_id', config.sharedId);
  if (config.from) params.append('from', config.from);
  if (config.auth) params.append('auth', config.auth);
  
  // 添加时间戳参数防止缓存
  params.append('_t', new Date().getTime());
  
  return `${config.baseUrl}?${params.toString()}`;
});

// 选择助手
const selectAssistant = (type) => {
  if (selectedAssistant.value === type) {
    // 如果选择的是同一个助手，增加计数器强制重新加载
    reloadCounter.value++;
  } else {
    selectedAssistant.value = type;
  }
};

// 手动重新加载iframe
const reloadIframe = () => {
  reloadCounter.value++;
};
</script>

<style scoped>
.professional-assistant-view {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  background-color: #f8fafc;
}

.assistant-selector {
  padding: 2rem;
  background: white;
  border-bottom: 1px solid #e2e8f0;
}

.selector-header {
  text-align: center;
  margin-bottom: 2rem;
}

.selector-title {
  font-size: 1.875rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 0.5rem 0;
}

.selector-description {
  color: #64748b;
  font-size: 1rem;
  margin: 0;
}

.assistant-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  max-width: 800px;
  margin: 0 auto;
}

.assistant-card {
  position: relative;
  background: white;
  border: 2px solid #e2e8f0;
  border-radius: 1rem;
  padding: 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: flex-start;
  gap: 1rem;
}

.assistant-card:hover {
  border-color: #3b82f6;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
  transform: translateY(-2px);
}

.assistant-card.active {
  border-color: #3b82f6;
  background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
}

.card-icon {
  flex-shrink: 0;
  width: 3rem;
  height: 3rem;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  border-radius: 0.75rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.card-icon .icon {
  width: 1.5rem;
  height: 1.5rem;
  color: white;
}

.card-content {
  flex: 1;
}

.card-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 0.5rem 0;
}

.card-description {
  color: #64748b;
  font-size: 0.875rem;
  line-height: 1.5;
  margin: 0;
}

.card-indicator {
  position: absolute;
  top: 1rem;
  right: 1rem;
  width: 1.5rem;
  height: 1.5rem;
  background: #10b981;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.check-icon {
  width: 1rem;
  height: 1rem;
  color: white;
}

.iframe-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.iframe-header {
  background: white;
  padding: 1rem 2rem;
  border-bottom: 1px solid #e2e8f0;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.header-info {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.header-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
}

.assistant-badge {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  border: 1px solid #bfdbfe;
}

.badge-icon {
  width: 1rem;
  height: 1rem;
  color: #3b82f6;
}

.badge-text {
  font-size: 0.875rem;
  font-weight: 500;
  color: #1e40af;
}

.change-assistant-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: #f1f5f9;
  border: 1px solid #e2e8f0;
  border-radius: 0.5rem;
  color: #64748b;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.change-assistant-btn:hover {
  background: #e2e8f0;
  color: #475569;
}

.btn-icon {
  width: 1rem;
  height: 1rem;
}

.iframe-wrapper {
  flex: 1;
  padding: 2rem;
  min-height: 0;
}

.iframe-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  min-height: 400px;
  background: white;
  border-radius: 0.75rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  border: 1px solid #e2e8f0;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #e2e8f0;
  border-radius: 50%;
  border-top-color: #3b82f6;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  color: #64748b;
  font-size: 1rem;
}

.iframe-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  min-height: 400px;
  background: white;
  border-radius: 0.75rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  border: 1px solid #e2e8f0;
  padding: 2rem;
  text-align: center;
}

.error-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: #fee2e2;
  color: #ef4444;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 1rem;
}

.error-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 0.5rem 0;
}

.error-message {
  color: #64748b;
  font-size: 0.875rem;
  margin: 0 0 1.5rem 0;
  max-width: 400px;
}

.retry-btn {
  padding: 0.5rem 1rem;
  background-color: #3b82f6;
  color: white;
  border: none;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.retry-btn:hover {
  background-color: #2563eb;
}

.chat-iframe {
  width: 100%;
  height: 100%;
  min-height: 600px;
  border-radius: 0.75rem;
  background-color: white;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  border: 1px solid #e2e8f0;
}

.empty-state {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
}

.empty-content {
  text-align: center;
  max-width: 400px;
}

.empty-icon {
  width: 4rem;
  height: 4rem;
  color: #94a3b8;
  margin: 0 auto 1.5rem auto;
}

.empty-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #475569;
  margin: 0 0 0.5rem 0;
}

.empty-description {
  color: #64748b;
  font-size: 1rem;
  margin: 0;
}

@media (max-width: 768px) {
  .assistant-selector {
    padding: 1rem;
  }
  
  .assistant-cards {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .iframe-header {
    padding: 1rem;
    flex-direction: row; /* 改为row以保持在移动端也横向排列 */
    gap: 1rem;
    align-items: center;
  }
  
  .header-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
  
  .iframe-wrapper {
    padding: 1rem;
  }
  
  .chat-iframe {
    min-height: 500px;
  }
}
</style>