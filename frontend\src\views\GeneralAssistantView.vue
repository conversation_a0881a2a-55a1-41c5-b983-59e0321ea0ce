<template>
  <div class="iframe-view">
    <iframe
      :src="iframeUrl"
      class="chat-iframe"
      frameborder="0"
    ></iframe>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import { iframeConfig } from '../config';

// 根据配置生成iframe的URL
const iframeUrl = computed(() => {
  const config = iframeConfig.rules;
  const params = new URLSearchParams();
  
  if (config.sharedId) params.append('shared_id', config.sharedId);
  if (config.from) params.append('from', config.from);
  if (config.auth) params.append('auth', config.auth);
  
  return `${config.baseUrl}?${params.toString()}`;
});
</script>

<style scoped>
.iframe-view {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
}

.chat-iframe {
  width: 100%;
  height: 100%;
  min-height: 600px;
  border-radius: 0.75rem;
  background-color: white;
  box-shadow: var(--shadow);
}
</style>