<template>
  <div id="app-wrapper">
    <div class="app-container">
      <!-- Sidebar -->
      <aside class="sidebar" :class="{ 'collapsed': store.sidebarCollapsed }">
        <div class="sidebar-header">
          <h2 class="logo" v-if="!store.sidebarCollapsed">AI Chat</h2>
          <h2 class="logo-icon" v-else>AI</h2>
          <button class="collapse-btn" @click="store.toggleSidebar">
            <ChevronLeft v-if="!store.sidebarCollapsed" />
            <ChevronRight v-else />
          </button>
        </div>
        
        <div class="sidebar-content">
          <nav class="sidebar-nav">
            <router-link
              v-for="route in routes"
              :key="route.name"
              :to="{ name: route.name }"
              custom
              v-slot="{ navigate, isActive }"
            >
              <button 
                class="nav-item" 
                :class="{ active: isActive }"
                @click="navigate"
              >
                <component :is="getIconComponent(route.meta?.icon)" class="nav-icon" />
                <span class="nav-text" v-if="!store.sidebarCollapsed">{{ route.meta?.title }}</span>
              </button>
            </router-link>
          </nav>
        </div>
        
        <div class="sidebar-footer" v-if="!store.sidebarCollapsed">
          <button class="graph-btn" @click="openGraphView">
            <Network class="graph-icon" />
            <span>查看图谱</span>
          </button>
        </div>
        <div class="sidebar-footer-icon" v-else>
          <button class="graph-btn-icon" title="查看图谱" @click="openGraphView">
            <Network class="graph-icon" />
          </button>
        </div>
      </aside>
      
      <!-- Main Content -->
      <main class="main-content">
        <header class="content-header">
          <h1 class="page-title">{{ $route.meta?.title || '欢迎使用' }}</h1>
        </header>
        
        <div class="iframe-container">
          <router-view v-slot="{ Component }">
            <transition name="fade" mode="out-in">
              <component :is="Component" />
            </transition>
          </router-view>
        </div>
      </main>
    </div>
  </div>
</template>

<script setup>
import { useIframeStore } from './stores/iframe';
import { MessageSquare, Bot, Shield, ChevronLeft, ChevronRight, Network } from 'lucide-vue-next';
import routes from './router';
import { systemConfig } from './config';

const store = useIframeStore();

// 根据图标名称字符串返回对应的组件
function getIconComponent(iconName) {
  if (!iconName) return MessageSquare;

  const iconMap = {
    MessageSquare,
    Bot,
    Shield,
    Network
  };
  return iconMap[iconName] || MessageSquare;
}

// 打开图谱查看页面
function openGraphView() {
  window.open(systemConfig.graphUrl, '_blank');
}
</script>

<style>
/* 重置全局样式影响 */
#app-wrapper {
  width: 100%;
  height: 100vh;
  overflow: hidden;
}

/* 覆盖main.css中的限制性样式 */
#app {
  max-width: none !important;
  margin: 0 !important;
  padding: 0 !important;
  display: block !important;
  width: 100% !important;
  height: 100vh !important;
}

:root {
  --primary: #6366f1;
  --primary-hover: #4f46e5;
  --sidebar-bg: #ffffff;
  --sidebar-width: 240px;
  --sidebar-collapsed-width: 70px;
  --text-primary: #1e293b;
  --text-secondary: #64748b;
  --border-color: #e2e8f0;
  --bg-hover: #f1f5f9;
  --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --transition: all 0.3s ease;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

body {
  background-color: #f8fafc;
  color: var(--text-primary);
  margin: 0;
  padding: 0;
  display: block !important;
  place-items: initial !important;
}

.app-container {
  display: flex;
  min-height: 100vh;
  width: 100%;
}

/* Sidebar Styles */
.sidebar {
  width: var(--sidebar-width);
  background-color: var(--sidebar-bg);
  border-right: 1px solid var(--border-color);
  display: flex;
  flex-direction: column;
  transition: var(--transition);
  box-shadow: var(--shadow);
  z-index: 10;
}

.sidebar.collapsed {
  width: var(--sidebar-collapsed-width);
}

.sidebar-header {
  padding: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid var(--border-color);
}

.logo {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--primary);
}

.logo-icon {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--primary);
  text-align: center;
}

.collapse-btn {
  background: none;
  border: none;
  cursor: pointer;
  color: var(--text-secondary);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem;
  border-radius: 0.375rem;
  transition: var(--transition);
}

.collapse-btn:hover {
  background-color: var(--bg-hover);
  color: var(--text-primary);
}

.sidebar-content {
  flex: 1;
  padding: 1rem 0;
  overflow-y: auto;
}

.sidebar-nav {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  padding: 0 0.75rem;
}

.nav-item {
  display: flex;
  align-items: center;
  padding: 0.75rem;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: var(--transition);
  border: none;
  background: none;
  color: var(--text-secondary);
  width: 100%;
  text-align: left;
}

.nav-item:hover {
  background-color: var(--bg-hover);
  color: var(--text-primary);
}

.nav-item.active {
  background-color: var(--primary);
  color: white;
}

.nav-icon {
  width: 1.25rem;
  height: 1.25rem;
}

.nav-text {
  margin-left: 0.75rem;
  font-size: 0.875rem;
  font-weight: 500;
}

.sidebar-footer {
  padding: 1rem;
  border-top: 1px solid var(--border-color);
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.graph-btn {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  width: 100%;
  padding: 0.75rem;
  border-radius: 0.5rem;
  background: none;
  border: none;
  cursor: pointer;
  color: var(--text-secondary);
  transition: var(--transition);
  text-align: left;
}

.graph-btn:hover {
  background-color: var(--bg-hover);
  color: var(--text-primary);
}

.graph-icon {
  width: 1.25rem;
  height: 1.25rem;
}

.graph-btn-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 0.5rem;
  background: none;
  border: none;
  cursor: pointer;
  color: var(--text-secondary);
  transition: var(--transition);
  margin-bottom: 0.5rem;
}

.graph-btn-icon:hover {
  background-color: var(--bg-hover);
  color: var(--text-primary);
}

.sidebar-footer-icon {
  padding: 1rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  border-top: 1px solid var(--border-color);
}



/* Main Content Styles */
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  overflow: hidden;
}

.content-header {
  background: white;
  padding: 1.5rem 2rem;
  border-bottom: 1px solid var(--border-color);
  box-shadow: var(--shadow);
}

.page-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.iframe-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
  overflow: hidden;
}

/* Transition Styles */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .sidebar {
    position: fixed;
    left: 0;
    top: 0;
    height: 100vh;
    z-index: 1000;
    transform: translateX(-100%);
  }
  
  .sidebar:not(.collapsed) {
    transform: translateX(0);
  }
  
  .main-content {
    margin-left: 0;
  }
  
  .content-header {
    padding: 1rem;
  }
}
</style>
