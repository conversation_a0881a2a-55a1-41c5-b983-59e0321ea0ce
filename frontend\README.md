# Write Helper 前端项目

这个项目是一个基于Vue 3的单页应用，用于嵌入和展示AI助手的iframe内容。

## 项目设置

### 安装依赖
```bash
npm install
```

### 开发模式下运行
```bash
npm run dev
```

### 构建生产版本
```bash
npm run build
```

### 预览构建后的应用
```bash
npm run preview
```

## 配置文件

项目使用配置文件来管理iframe的URL和参数，配置文件位于 `src/config.js`。

### 配置说明

在构建项目后，可以通过修改 `dist/assets/` 目录中的配置文件来适配不同的环境，无需重新构建应用。

配置文件结构如下：

```javascript
// iframe相关配置
export const iframeConfig = {
  // 规章制度问答助手
  rules: {
    baseUrl: 'http://your-server/chat/share',  // iframe的基础URL
    sharedId: 'your-shared-id',                // 共享ID参数
    from: 'agent',                             // 来源参数
    auth: 'your-auth-token'                    // 认证参数
  },
  // 安全报告助手
  safety: {
    // 同上
  }
};

// 其他系统配置
export const systemConfig = {
  title: 'AI写作助手',      // 网页标题
  defaultLocale: 'zh-CN'    // 默认语言
};
```

### 在生产环境中修改配置

1. 在服务器上找到构建后的配置文件（通常位于 `dist/assets/` 目录）
2. 编辑该文件，修改相应的URL和参数
3. 保存文件并刷新应用

## 项目结构

- `src/views/`: 包含各个页面视图组件
- `src/components/`: 公共组件
- `src/stores/`: Pinia 状态管理
- `src/router/`: 路由配置
- `src/assets/`: 静态资源
- `src/config.js`: 配置文件

## 注意事项

- iframe URL的参数会根据配置文件动态生成
- 侧边栏可以通过点击收缩按钮展开/收起
- 确保内部网络可以访问配置的iframe URL
